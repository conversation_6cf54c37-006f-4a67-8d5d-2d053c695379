# --- Do not remove these libs ---
from freqtrade.strategy import IStrategy
from pandas import DataFrame

# --------------------------------
#   Strategy for ETC:
#   - Buy when price is at or below 12 USDT
#   - Sell when price is at or above 18 USDT
#
#   Author: Your Name
#   Version: 1.0
# --------------------------------


class EtcHoldStrategy(IStrategy):
    """
    ETC Long Term Hold Strategy
    Buy <= 12 USDT, Sell >= 18 USDT
    """

    # --- 策略配置 ---
    # ROI table: 我们把它设置得非常高，这样策略就不会因为时间而自动卖出。
    # 我们希望完全由自定义的卖出信号（价格达到18U）来控制。
    minimal_roi = {
        "0": 100  # 100 表示 10000% 收益，基本上等于禁用
    }

    # Stoploss: 这是一个重要的风险控制。-0.10 表示如果价格下跌10%，就自动卖出止损。
    # 如果你完全不想止损，可以设置为 -0.99，但这风险极高，不推荐。
    stoploss = -0.99

    # Trailing stop: 禁用动态止损
    trailing_stop = False

    # Timeframe: 使用日线（1d）图表，符合长线策略，减少频繁检查的需要。
    # 你也可以改成 '4h' 或 '1h' 来增加检查频率。
    timeframe = "30m"

    # --- 策略信号 ---
    # 设置为 True 是为了让 populate_exit_trend 中的卖出信号生效。
    use_exit_signal = True
    exit_profit_only = False
    process_only_new_candles = True

    # --- populate_indicators ---
    # 这个策略非常简单，不需要计算任何技术指标（如RSI, MACD），
    # 所以这个函数保持原样即可。
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        return dataframe

    # --- 买入信号 (Entry Trend) ---
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        买入条件：
        1. 当前交易对是 ETC/USDT
        2. 当前K线的收盘价 <= 12
        """
        dataframe.loc[
            (
                (dataframe["close"] <= 13)  # 价格条件：收盘价低于或等于 12
                & (dataframe["volume"] > 0)  # 确保K线是活跃的
            ),
            "enter_long",
        ] = 1

        return dataframe

    # --- 卖出信号 (Exit Trend) ---
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        卖出条件：
        1. 当前交易对是 ETC/USDT
        2. 当前K线的收盘价 >= 18
        """
        dataframe.loc[
            (
                (dataframe["close"] >= 18)  # 价格条件：收盘价高于或等于 18
                & (dataframe["volume"] > 0)  # 确保K线是活跃的
            ),
            "exit_long",
        ] = 1

        return dataframe
