import logging
import warnings
from datetime import datetime, timedelta
from functools import reduce
from typing import TYPE_CHECKING

import pandas as pd
import pandas_ta as pta
import talib.abstract as ta
from freqtrade.strategy import BooleanParameter, DecimalParameter, IntParameter
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame

if TYPE_CHECKING:
    from freqtrade.persistence import Trade

warnings.simplefilter(action="ignore", category=RuntimeWarning)

logger = logging.getLogger(__name__)


class E0V1E_Short(IStrategy):
    """
    E0V1E 做空策略

    策略核心思想:
    基于均值回归的5分钟短线做空策略，在多重技术指标确认的超买状态下入场，
    通过动态出场机制和严格的风险管理来获取短期回调收益。

    主要特性:
    1. 专注做空交易，与E0V1E_Enhanced形成对冲
    2. 在超买状态下入场做空
    3. 在超卖状态下平仓获利
    4. 优化的风险管理和动态止损
    5. 增强的技术指标和成交量确认
    6. 智能的出场逻辑和风险控制
    7. 市场环境过滤机制
    8. 全面参数化，针对做空优化
    """

    # === hyperopt ===
    optimize_entry = True
    optimize_leverage_threshold = True
    optimize_leverage_volume = True

    # === 基础配置 ===
    # Sell hyperspace params (for short entry):
    sell_params = {
        # === 市场环境过滤参数 ===
        "sell_market_24h_change_enable": True,  # 启用市场过滤
        "sell_market_24h_change_min": -20,
        "sell_market_24h_change_max": 30,
        # === ATR波动性过滤器 ===
        "sell_atr_pct_enable": True,  # 启用波动性过滤
        "sell_atr_pct_min": 0.5,
        "sell_atr_pct_max": 8.0,
        # === 成交量过滤器 ===
        "sell_volume_enable": True,  # 启用成交量过滤
        "sell_volume_factor": 1.2,
        # === 做空入场参数（超买条件）===
        "sell_cti": -0.05,  # 严格的动量条件（镜像买入的0.05）
        "sell_rsi": 79,  # 极度超买阈值（镜像买入的21）
        "sell_rsi_alt": 82,
        "sell_rsi_fast": 82,  # 极度超买的快速RSI
        "sell_rsi_fast_alt": 85,
        "sell_sma_ratio": 1.022,  # 价格必须高于均线（镜像买入的0.978）  
        "sell_sma_ratio_alt": 1.024,
        # === 精细入场条件 ===
        "sell_fastk_threshold": 90,  # FastK超买阈值
        "sell_rsi_fastk_combo_enable": True,  # 启用RSI+FastK组合
        "sell_bb_breakout_enable": True,  # 启用布林带突破确认
        "sell_volume_surge_enable": True,  # 启用成交量放大确认
        "sell_volume_surge_factor": 1.5,  # 成交量放大倍数
        # === 动态杠杆调整超参数 ===
        "sell_leverage_main_signal_multiplier": 1.0,
        "sell_leverage_alt_signal_multiplier": 0.4,
        "sell_leverage_atr_low_threshold": 1.65,
        "sell_leverage_atr_low_vol": 5.0,
        "sell_leverage_atr_high_threshold": 3.5,
        "sell_leverage_atr_high_vol": 2.0,
        "sell_leverage_atr_normal_vol": 3.0,
        # RSI极度超买杠杆增强
        "sell_leverage_rsi_extreme_enable": True,
        "sell_leverage_rsi_extreme_threshold": 79.0,  # RSI极度超买值(>)
        "sell_leverage_rsi_extreme_boost_factor": 1.4,
        # === 成交量异常调整超参数 ===
        "sell_leverage_volume_enable": True,
        "sell_leverage_volume_high_threshold": 2.75,
        "sell_leverage_volume_high_reduction_factor": 0.8,
    }

    # Exit short hyperspace params (for short exit):
    exit_short_params = {
        "exit_short_max_hold_hours": 11,
        "exit_short_profit_protection_high_threshold": 0.08,
        "exit_short_profit_protection_high_fastk": 20,  # 极度超卖平仓（镜像做多的80）
        "exit_short_cci_loss": 20,  # 低CCI风险平仓
        "exit_short_profit_protection_normal_fastk": 25,  # 正常超卖平仓
        "exit_short_rsi_bottom": 21,  # RSI底部平仓（镜像做多的79）
        "exit_short_atr_max_pct": 6.0,
        "exit_short_max_hold_profit_threshold": -0.052,
    }

    # Protection hyperspace params:
    protection_params = {
        "protection_cooldown_enable": False,
        "protection_cooldown_stop_duration": 48,
        "protection_stoploss_enable": True,
        "protection_stoploss_lookback_period": 24,
        "protection_stoploss_trade_limit": 1,
        "protection_stoploss_stop_duration": 60,
        "protection_maxdrawdown_enable": False,
        "protection_maxdrawdown_lookback_period": 144,
        "protection_maxdrawdown_trade_limit": 5,
        "protection_maxdrawdown_stop_duration": 120,
        "protection_maxdrawdown_max_allowed": 0.10,
        "protection_lowprofit_enable": False,
        "protection_lowprofit_lookback_period": 288,
        "protection_lowprofit_trade_limit": 8,
        "protection_lowprofit_stop_duration": 240,
        "protection_lowprofit_required_profit": 0.01,
    }

    minimal_roi = {}
    timeframe = "5m"
    process_only_new_candles = True
    startup_candle_count = 300
    
    # === 做空配置 ===
    can_short = True

    # === 订单配置 ===
    order_types = {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": False,
        "stoploss_on_exchange_interval": 60,
        "stoploss_on_exchange_market_ratio": 0.99,
    }

    # === 风险管理配置（做空调整）===
    stoploss = -0.1  # 做空止损（价格上涨10%）

    # 追踪止损配置（做空调整）
    trailing_stop = True
    trailing_stop_positive = 0.03  # 当价格从最低点上涨0.3%时，触发追踪止损
    trailing_stop_positive_offset = 0.15  # 利润达到15%后，才激活追踪止损
    trailing_only_offset_is_reached = True

    use_custom_stoploss = False

    # === 策略开关 ===
    sell_market_24h_change_enable = BooleanParameter(default=False, space="sell", optimize=False)
    sell_volume_enable = BooleanParameter(default=False, space="sell", optimize=False)
    sell_atr_pct_enable = BooleanParameter(default=False, space="sell", optimize=False)

    # === 市场环境过滤参数 ===
    sell_market_24h_change_min = DecimalParameter(
        -25.0, -5.0, default=-20.0, decimals=1, space="sell", optimize=optimize_entry
    )
    sell_market_24h_change_max = DecimalParameter(
        5.0, 35.0, default=30.0, decimals=1, space="sell", optimize=optimize_entry
    )

    # ATR波动性过滤
    sell_atr_pct_min = DecimalParameter(
        0.3, 1.0, default=0.5, decimals=1, space="sell", optimize=optimize_entry
    )
    sell_atr_pct_max = DecimalParameter(
        3.0, 10.0, default=8.0, decimals=1, space="sell", optimize=optimize_entry
    )

    # 成交量过滤
    sell_volume_factor = DecimalParameter(
        1.0, 2.0, default=1.2, decimals=1, space="sell", optimize=optimize_entry
    )

    # === 做空入场信号参数（优化版）===
    sell_rsi_fast = IntParameter(75, 90, default=82, space="sell", optimize=optimize_entry)
    sell_rsi = IntParameter(75, 90, default=79, space="sell", optimize=optimize_entry)
    
    # === 精细入场条件参数 ===
    sell_fastk_threshold = IntParameter(80, 95, default=90, space="sell", optimize=optimize_entry)
    sell_rsi_fastk_combo_enable = BooleanParameter(default=True, space="sell", optimize=False)
    sell_bb_breakout_enable = BooleanParameter(default=True, space="sell", optimize=False)
    sell_volume_surge_enable = BooleanParameter(default=True, space="sell", optimize=False)
    sell_volume_surge_factor = DecimalParameter(
        1.2, 3.0, default=1.5, decimals=1, space="sell", optimize=optimize_entry
    )
    sell_sma_ratio = DecimalParameter(
        1.01, 1.08, default=1.022, decimals=3, space="sell", optimize=optimize_entry
    )
    sell_cti = DecimalParameter(
        -0.5, 0.5, default=-0.05, decimals=2, space="sell", optimize=optimize_entry
    )

    # === 备用做空入场信号参数 ===
    sell_rsi_fast_alt = IntParameter(80, 95, default=85, space="sell", optimize=optimize_entry)
    sell_rsi_alt = IntParameter(78, 90, default=82, space="sell", optimize=optimize_entry)
    sell_sma_ratio_alt = DecimalParameter(
        1.02, 1.08, default=1.024, decimals=3, space="sell", optimize=optimize_entry
    )

    # === 做空出场信号参数（优化版）===
    exit_short_profit_protection_normal_fastk = IntParameter(
        10, 35, default=25, space="exit_short", optimize=True
    )
    exit_short_cci_loss = IntParameter(-40, 40, default=20, space="exit_short", optimize=True)
    exit_short_rsi_bottom = IntParameter(15, 30, default=21, space="exit_short", optimize=True)
    
    exit_short_atr_max_pct = DecimalParameter(
        3.0, 8.0, default=5.0, decimals=1, space="exit_short", optimize=optimize_entry
    )

    # 时间止损参数
    exit_short_max_hold_hours = IntParameter(4, 12, default=8, space="exit_short", optimize=True)
    exit_short_max_hold_profit_threshold = DecimalParameter(
        -0.08, -0.02, default=-0.05, decimals=3, space="exit_short", optimize=True
    )

    # 利润保护参数
    exit_short_profit_protection_high_threshold = DecimalParameter(
        0.03, 0.08, default=0.05, decimals=3, space="exit_short", optimize=True
    )
    exit_short_profit_protection_high_fastk = IntParameter(
        10, 30, default=20, space="exit_short", optimize=True
    )

    # === 动态杠杆调整超参数 ===
    sell_leverage_atr_normal_vol = DecimalParameter(
        2.0, 4.0, default=3.0, decimals=1, space="sell", optimize=optimize_leverage_volume
    )
    sell_leverage_atr_low_vol = DecimalParameter(
        4.0, 8.0, default=5.0, decimals=1, space="sell", optimize=optimize_leverage_volume
    )
    sell_leverage_atr_high_vol = DecimalParameter(
        1.0, 3.0, default=2.0, decimals=1, space="sell", optimize=optimize_leverage_volume
    )
    sell_leverage_atr_low_threshold = DecimalParameter(
        1.0, 2.0, default=1.5, decimals=1, space="sell", optimize=optimize_leverage_threshold
    )
    sell_leverage_atr_high_threshold = DecimalParameter(
        3.5, 5.5, default=3.5, decimals=1, space="sell", optimize=optimize_leverage_threshold
    )

    sell_leverage_main_signal_multiplier = DecimalParameter(
        1.2, 1.6, default=1.0, decimals=2, space="sell", optimize=optimize_leverage_volume
    )
    sell_leverage_alt_signal_multiplier = DecimalParameter(
        0.2, 0.6, default=0.33, decimals=2, space="sell", optimize=optimize_leverage_volume
    )

    # RSI极值杠杆调整（做空：极度超买）
    sell_leverage_rsi_extreme_enable = BooleanParameter(default=True, space="sell", optimize=False)
    sell_leverage_rsi_extreme_threshold = DecimalParameter(
        75.0, 85.0, default=79.0, space="sell", optimize=optimize_leverage_threshold
    )
    sell_leverage_rsi_extreme_boost_factor = DecimalParameter(
        1.3, 2.0, default=1.4, decimals=2, space="sell", optimize=optimize_leverage_volume
    )
    

    # 成交量杠杆调整
    sell_leverage_volume_enable = BooleanParameter(default=True, space="sell", optimize=False)
    sell_leverage_volume_high_threshold = DecimalParameter(
        2.0, 4.0, default=3.0, decimals=1, space="sell", optimize=optimize_leverage_threshold
    )
    sell_leverage_volume_high_reduction_factor = DecimalParameter(
        0.6, 0.9, default=0.8, decimals=2, space="sell", optimize=optimize_leverage_volume
    )

    # === 交易保护机制超参数 ===
    protection_stoploss_enable = BooleanParameter(default=True, space="protection", optimize=False)
    protection_stoploss_lookback_period = IntParameter(
        12, 48, default=24, space="protection", optimize=protection_stoploss_enable.value
    )
    protection_stoploss_trade_limit = IntParameter(
        1, 3, default=1, space="protection", optimize=protection_stoploss_enable.value
    )
    protection_stoploss_stop_duration = IntParameter(
        24, 120, default=60, space="protection", optimize=protection_stoploss_enable.value
    )

    @property
    def protections(self):
        """
        动态交易保护机制 - 基于超参数优化的智能保护策略
        """
        protection_list = []

        if self.protection_stoploss_enable.value:
            protection_list.append(
                {
                    "method": "StoplossGuard",
                    "lookback_period_candles": self.protection_stoploss_lookback_period.value,
                    "trade_limit": self.protection_stoploss_trade_limit.value,
                    "stop_duration_candles": self.protection_stoploss_stop_duration.value,
                    "only_per_pair": True,
                }
            )

        return protection_list

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """计算技术指标"""

        # === RSI系列指标 ===
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=14)
        dataframe["rsi_fast"] = ta.RSI(dataframe, timeperiod=4)
        dataframe["rsi_slow"] = ta.RSI(dataframe, timeperiod=20)

        # === 移动平均线 ===
        dataframe["sma_15"] = ta.SMA(dataframe, timeperiod=15)
        dataframe["ema_20"] = ta.EMA(dataframe, timeperiod=20)

        # === 趋势和动量指标 ===
        dataframe["cti"] = pta.cti(dataframe["close"], length=20)
        dataframe["cci"] = ta.CCI(dataframe, timeperiod=20)

        # === 随机指标 ===
        stoch_fast = ta.STOCHF(dataframe, 5, 3, 0, 3, 0)
        dataframe["fastk"] = stoch_fast["fastk"]
        dataframe["fastd"] = stoch_fast["fastd"]

        # === 波动性指标 ===
        dataframe["atr"] = ta.ATR(dataframe, timeperiod=14)
        dataframe["atr_pct"] = (dataframe["atr"] / dataframe["close"]) * 100

        # === 成交量指标 ===
        dataframe["volume_sma"] = ta.SMA(dataframe["volume"], timeperiod=20)
        dataframe["volume_ratio"] = dataframe["volume"] / dataframe["volume_sma"]

        # === 市场环境指标 ===
        dataframe["24h_change_pct"] = (
            (dataframe["close"] - dataframe["close"].shift(288)) / dataframe["close"].shift(288)
        ) * 100

        # === 布林带指标 ===
        bollinger = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2.0, nbdevdn=2.0, matype=0)
        dataframe["bb_lowerband"] = bollinger["lowerband"]
        dataframe["bb_middleband"] = bollinger["middleband"]
        dataframe["bb_upperband"] = bollinger["upperband"]
        dataframe["bb_percent"] = (dataframe["close"] - dataframe["bb_lowerband"]) / (
            dataframe["bb_upperband"] - dataframe["bb_lowerband"]
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """定义做空入场信号"""
        short_conditions = []
        dataframe.loc[:, "enter_tag"] = ""

        # === 市场环境过滤器 ===
        market_filter = True
        if self.sell_market_24h_change_enable.value:
            market_filter = (
                (dataframe["24h_change_pct"].notna())
                & (dataframe["24h_change_pct"] > self.sell_market_24h_change_min.value)
                & (dataframe["24h_change_pct"] < self.sell_market_24h_change_max.value)
            )

        # === ATR波动性过滤器 ===
        atr_filter = True
        if self.sell_atr_pct_enable.value:
            atr_filter = (dataframe["atr_pct"] > self.sell_atr_pct_min.value) & (
                dataframe["atr_pct"] < self.sell_atr_pct_max.value
            )

        # === 成交量过滤器 ===
        volume_filter = True
        if self.sell_volume_enable.value:
            volume_filter = dataframe["volume_ratio"] > self.sell_volume_factor.value

        # === 基础过滤条件组合 ===
        base_filter = market_filter & atr_filter & volume_filter

        # 主信号: 精细化做空逻辑（RSI + FastK + 布林带）
        rsi_condition = dataframe["rsi"] > self.sell_rsi.value
        rsi_fast_condition = dataframe["rsi_fast"] > self.sell_rsi_fast.value
        cti_condition = dataframe["cti"] < self.sell_cti.value
        sma_condition = dataframe["close"] > dataframe["sma_15"] * self.sell_sma_ratio.value
        
        # RSI + FastK 组合条件
        if self.sell_rsi_fastk_combo_enable.value:
            fastk_condition = dataframe["fastk"] > self.sell_fastk_threshold.value
            combo_condition = rsi_condition & rsi_fast_condition & fastk_condition
        else:
            combo_condition = rsi_condition & rsi_fast_condition
            
        # 布林带突破确认
        if self.sell_bb_breakout_enable.value:
            bb_condition = dataframe["bb_percent"] > 0.95  # 接近或突破上轨
        else:
            bb_condition = True
            
        # 成交量放大确认
        if self.sell_volume_surge_enable.value:
            volume_surge_condition = dataframe["volume_ratio"] > self.sell_volume_surge_factor.value
        else:
            volume_surge_condition = True

        main_short_entry = (
            base_filter
            & combo_condition
            & cti_condition
            & sma_condition
            & bb_condition
            & volume_surge_condition
        )

        short_conditions.append(main_short_entry)
        dataframe.loc[main_short_entry, "enter_tag"] += "main_short"

        # 备用信号: 更激进的做空（降低部分条件）
        alt_rsi_condition = dataframe["rsi"] > self.sell_rsi_alt.value
        alt_rsi_fast_condition = dataframe["rsi_fast"] > self.sell_rsi_fast_alt.value
        alt_sma_condition = dataframe["close"] > dataframe["sma_15"] * self.sell_sma_ratio_alt.value
        
        alt_short_entry = (
            base_filter
            & alt_rsi_condition
            & alt_rsi_fast_condition
            & alt_sma_condition
            & (dataframe["bb_percent"] > 0.9)  # 稍微放宽布林带条件
        )

        short_conditions.append(alt_short_entry)
        dataframe.loc[alt_short_entry, "enter_tag"] += "alt_short"

        # === 应用入场条件 ===
        if short_conditions:
            dataframe.loc[reduce(lambda x, y: x | y, short_conditions), "enter_short"] = 1

        return dataframe

    def custom_exit(
        self,
        pair: str,
        trade: "Trade",
        current_time: "datetime",
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        动态做空出场逻辑
        根据不同的入场原因实现智能平仓
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()

        # === 时间止损检查 ===
        if current_time - trade.open_date_utc > timedelta(hours=self.exit_short_max_hold_hours.value):
            if current_profit > self.exit_short_max_hold_profit_threshold.value:
                return "max_hold_hours"

        # 盈利时的出场策略（做空盈利 = 价格下跌）
        if current_profit > 0:
            # 高利润保护机制
            if current_profit > self.exit_short_profit_protection_high_threshold.value:
                if current_candle["fastk"] < self.exit_short_profit_protection_high_fastk.value:
                    return "high_profit_protection"

            # 标准盈利出场
            if current_candle["fastk"] < self.exit_short_profit_protection_normal_fastk.value:
                return "normal_profit_protection"

            # 针对主信号的特定出场
            if "main_short" in str(trade.enter_tag):
                if current_candle["rsi"] < self.exit_short_rsi_bottom.value:
                    return "主信号保护"

        # 风险控制出场
        # 小幅亏损时的保护性出场
        if current_profit > -0.05:
            if current_candle["cci"] < self.exit_short_cci_loss.value:
                return "小幅亏损风险"

        # 趋势反转信号（做空的反转 = 超卖反弹）
        if current_profit > -0.03:
            if (
                current_candle["rsi"] < 30
                and current_candle["fastk"] < 20
                and current_candle["fastd"] < 25
            ):
                return "趋势反转信号"

        # 波动性异常退出
        if current_candle["atr_pct"] > self.exit_short_atr_max_pct.value * 1.5:
            if current_profit > -0.08:
                return "波动性异常退出"

        return None

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        静态出场信号（主要依赖custom_exit）
        """
        dataframe.loc[:, ["exit_short", "exit_tag"]] = (0, None)
        return dataframe

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time: datetime,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> bool:
        """
        交易确认函数 - 做空交易的最后一道风险控制
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return False

        current_candle = dataframe.iloc[-1].squeeze()

        # 确保关键指标存在且有效
        required_indicators = ["rsi", "rsi_fast", "atr_pct", "volume_ratio"]
        for indicator in required_indicators:
            if indicator not in current_candle or pd.isna(current_candle[indicator]):
                return False

        # 最终风险检查
        if current_candle["atr_pct"] > 10.0:  # 极端波动
            return False

        if current_candle["rsi"] < 10 or current_candle["rsi"] > 90:  # 极端RSI
            return False

        # 确保不在极端超卖状态（做空时避免）
        if current_candle["rsi"] < 20 and current_candle["fastk"] < 10:
            return False

        return True

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态杠杆调整 - 基于超参数优化的智能杠杆策略（做空版）
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return 1.0

        current_candle = dataframe.iloc[-1].squeeze()

        # === 第一步: 根据波动性确定基础杠杆 ===
        base_leverage = self.sell_leverage_atr_normal_vol.value

        if "atr_pct" in current_candle and not pd.isna(current_candle["atr_pct"]):
            atr_pct = current_candle["atr_pct"]

            if atr_pct > self.sell_leverage_atr_high_threshold.value:
                base_leverage = self.sell_leverage_atr_high_vol.value
            elif atr_pct < self.sell_leverage_atr_low_threshold.value:
                base_leverage = self.sell_leverage_atr_low_vol.value

        # === 第二步: 根据入场信号类型调整杠杆 ===
        signal_multiplier = 1.0

        if "main_" in entry_tag:
            signal_multiplier = self.sell_leverage_main_signal_multiplier.value
        elif "alt_" in entry_tag:
            signal_multiplier = self.sell_leverage_alt_signal_multiplier.value

        adjusted_leverage = base_leverage * signal_multiplier

        # === 第三步: RSI极值加仓机制 ===
        if (
            self.sell_leverage_rsi_extreme_enable.value
            and "rsi" in current_candle
            and not pd.isna(current_candle["rsi"])
        ):
            rsi = current_candle["rsi"]
            # RSI > 79时大幅增加杠杆（极度超买加码）
            if side == "short" and rsi > self.sell_leverage_rsi_extreme_threshold.value:
                adjusted_leverage *= self.sell_leverage_rsi_extreme_boost_factor.value
                
        # === 第四步: 最终杠杆限制和安全检查 ===
        final_leverage = max(1.0, min(round(adjusted_leverage, 2), max_leverage))

        # 额外的安全检查
        if "atr_pct" in current_candle and current_candle["atr_pct"] > 8.0:
            final_leverage = min(final_leverage, 1.5)

        return final_leverage

    def custom_stake_amount(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_stake: float,
        min_stake: float,
        max_stake: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态仓位管理 - 支持做空仓位策略
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return proposed_stake

        current_candle = dataframe.iloc[-1].squeeze()

        # 基础仓位
        base_stake = proposed_stake

        # 根据信号强度调整仓位
        if "main_" in entry_tag:
            position_multiplier = 1.0
        elif "alt_" in entry_tag:
            position_multiplier = 0.7
        else:
            position_multiplier = 0.8

        # 根据市场波动性调整仓位
        if "atr_pct" in current_candle:
            atr_pct = current_candle["atr_pct"]
            if atr_pct > 5.0:
                position_multiplier *= 1  # 高波动时保持仓位
            elif atr_pct < 1.0:
                position_multiplier *= 2  # 低波动时可以适当增加仓位

        # 根据RSI极值调整仓位（做空：极度超买时增加仓位）
        if "rsi" in current_candle:
            rsi = current_candle["rsi"]
            if side == "short" and rsi > 80:
                position_multiplier *= 1.2

        final_stake = base_stake * position_multiplier
        return max(min_stake, min(final_stake, max_stake))