import logging
import warnings
from datetime import datetime, timedelta
from functools import reduce
from typing import TYPE_CHECKING

import pandas as pd
import pandas_ta as pta
import talib.abstract as ta
from freqtrade.strategy import BooleanParameter, DecimalParameter, IntParameter
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame

if TYPE_CHECKING:
    from freqtrade.persistence import Trade

warnings.simplefilter(action="ignore", category=RuntimeWarning)

logger = logging.getLogger(__name__)


class E0V1E_ShortOnly(IStrategy):
    """
    E0V1E 纯做空策略

    策略核心思想:
    基于均值回归的5分钟短线做空策略，在多重技术指标确认的超买状态下入场，
    通过动态出场机制和严格的风险管理来获取短期回调收益。

    主要特性:
    1. 纯做空交易策略
    2. 统一的入场逻辑，基于超买回调
    3. 优化的风险管理和动态止损
    4. 增强的技术指标和成交量确认
    5. 智能的出场逻辑和风险控制
    6. 市场环境过滤机制
    7. 全面参数化，专门优化做空交易
    """

    # === hyperopt ===
    optimize_entry = True
    optimize_leverage_threshold = True
    optimize_leverage_volume = True

    # === 基础配置 ===
    # Short hyperspace params: 做空策略的超参数配置
    short_params = {
        # === 市场环境过滤参数 ===
        # 启用24小时价格变化过滤器，避免在极端市场条件下交易
        "short_market_24h_change_enable": True,
        # 24小时价格变化最小值：-9.2%（避免在急跌市场做空，防止追跌）
        "short_market_24h_change_min": -9.2,  # 反转原来的max，做空时避免追跌
        # 24小时价格变化最大值：20%（在上涨市场中寻找做空机会）
        "short_market_24h_change_max": 20,  # 反转原来的min，做空时利用上涨回调
        # === ATR波动性过滤器 ===
        # 启用ATR波动性过滤，确保在适度波动的市场中交易
        "short_atr_pct_enable": True,
        # ATR最小波动率：1.0%（避免在过于平静的市场交易，确保有足够波动性）
        "short_atr_pct_min": 1.0,
        # ATR最大波动率：6.0%（避免在过于剧烈波动的市场交易，控制风险）
        "short_atr_pct_max": 6.0,
        # === 成交量过滤器 ===
        # 启用成交量过滤，确保有足够的流动性支持交易
        "short_volume_enable": True,
        # 成交量倍数：1.0（当前成交量需要大于20日平均成交量，确保流动性）
        "short_volume_factor": 1.0,
        # === 做空入场参数 ===
        # CTI指标阈值：-0.05（做空时需要CTI为正值，表示上涨动量存在）
        "short_cti": -0.05,  # 反转原来的0.05，做空时寻找正动量
        # 主RSI阈值：79（做空时RSI需要低于此值，避免在极度超买时入场）
        "short_rsi": 90,  # 调整到90，允许在极度超买时做空
        # 备用RSI阈值：78（备用信号的RSI阈值，稍微保守一些）
        "short_rsi_alt": 85,  # 调整到85，允许在高RSI时触发备用做空信号
        # 快速RSI阈值：55（做空时快速RSI需要高于此值，确认短期超买）- 调整为更容易触发
        "short_rsi_fast": 55,  # 从62调整到55，更容易触发做空信号
        # 备用快速RSI阈值：60（备用信号的快速RSI阈值）- 调整为更容易触发
        "short_rsi_fast_alt": 60,  # 从73调整到60，更容易触发做空信号
        # SMA比率：1.025（做空时价格需要高于SMA的倍数，确认价格在均线上方）
        "short_sma_ratio": 1.025,
        # 备用SMA比率：1.03（备用信号的SMA比率，要求更高的价格位置）
        "short_sma_ratio_alt": 1.03,
        # === 动态杠杆调整超参数 ===
        # 主信号杠杆倍数：1.0（主信号使用标准杠杆，信心较高）
        "short_leverage_main_signal_multiplier": 1.0,
        # 备用信号杠杆倍数：0.4（备用信号使用较低杠杆，降低风险）
        "short_leverage_alt_signal_multiplier": 0.4,
        # 低波动率阈值：1.65%（ATR低于此值认为是低波动市场）
        "short_leverage_atr_low_threshold": 1.65,
        # 低波动时杠杆：5.0（低波动时可以使用较高杠杆，风险相对较小）
        "short_leverage_atr_low_vol": 5.0,
        # 高波动率阈值：3.5%（ATR高于此值认为是高波动市场）
        "short_leverage_atr_high_threshold": 3.5,
        # 高波动时杠杆：2.0（高波动时使用较低杠杆，控制风险）
        "short_leverage_atr_high_vol": 2.0,
        # 正常波动时杠杆：3.0（正常波动时的标准杠杆设置）
        "short_leverage_atr_normal_vol": 3.0,
        # 启用RSI极值杠杆调整（在极度超买时可以增加杠杆）
        "short_leverage_rsi_extreme_enable": True,
        # RSI极度超买阈值：79.0（RSI高于此值时认为极度超买，可增加杠杆）
        "short_leverage_rsi_extreme_threshold": 79.0,  # RSI极度超买值(>)
        # RSI极值杠杆增强倍数：1.4（极度超买时杠杆增强倍数）
        "short_leverage_rsi_extreme_boost_factor": 1.4,
        # 启用成交量异常杠杆调整（异常放量时降低杠杆）
        "short_leverage_volume_enable": True,
        # 成交量异常阈值：2.75（成交量超过平均值的此倍数时认为异常）
        "short_leverage_volume_high_threshold": 2.75,
        # 成交量异常时杠杆降低倍数：0.8（异常放量时降低杠杆以控制风险）
        "short_leverage_volume_high_reduction_factor": 0.8,
    }

    # Sell hyperspace params (出场参数): 做空策略的出场配置
    sell_params = {
        # 最大持仓时间：11小时（避免长时间持仓，降低隔夜风险）
        "sell_max_hold_hours": 11,
        # 高利润保护阈值：8%（利润达到8%时启动高利润保护机制）
        "sell_profit_protection_high_threshold": 0.08,
        # 高利润保护FastK阈值：25（做空时FastK低于25时出场，反转原来的75）
        "sell_profit_protection_high_fastk": 25,  # 反转原来的75，做空时低FastK出场
        # CCI亏损出场阈值：-75（做空时CCI低于-75时出场，反转原来的75）
        "sell_cci_loss": -75,  # 反转原来的75，做空时负CCI出场
        # 标准利润保护FastK阈值：29（做空时FastK低于29时出场，反转原来的71）
        "sell_profit_protection_normal_fastk": 29,  # 反转原来的71，做空时低FastK出场
        # RSI峰值出场阈值：19（做空时RSI低于19时出场，反转原来的81）
        "sell_rsi_peak": 19,  # 反转原来的81，做空时低RSI出场
        # ATR最大波动率：6.0%（波动率过高时强制出场）
        "sell_atr_max_pct": 6.0,
        # 最大持仓亏损阈值：-5.2%（持仓时间过长且亏损超过此值时出场）
        "sell_max_hold_profit_threshold": -0.052,
    }

    # Protection hyperspace params:
    protection_params = {
        "protection_cooldown_enable": False,
        "protection_cooldown_stop_duration": 48,
        "protection_stoploss_enable": True,
        "protection_stoploss_lookback_period": 24,
        "protection_stoploss_trade_limit": 1,
        "protection_stoploss_stop_duration": 60,
        "protection_maxdrawdown_enable": False,
        "protection_maxdrawdown_lookback_period": 144,
        "protection_maxdrawdown_trade_limit": 5,
        "protection_maxdrawdown_stop_duration": 120,
        "protection_maxdrawdown_max_allowed": 0.10,
        "protection_lowprofit_enable": False,
        "protection_lowprofit_lookback_period": 288,
        "protection_lowprofit_trade_limit": 8,
        "protection_lowprofit_stop_duration": 240,
        "protection_lowprofit_required_profit": 0.01,
    }

    minimal_roi = {}  # 禁用基于时间的ROI，完全依赖动态出场
    timeframe = "5m"
    process_only_new_candles = True
    startup_candle_count = 300

    # === 订单配置 ===
    order_types = {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": False,
        "stoploss_on_exchange_interval": 60,
        "stoploss_on_exchange_market_ratio": 0.99,
    }

    # === 风险管理配置 ===
    stoploss = -0.1  # 统一止损水平

    # 追踪止损配置
    trailing_stop = True
    trailing_stop_positive = 0.03
    trailing_stop_positive_offset = 0.15
    trailing_only_offset_is_reached = True

    use_custom_stoploss = False

    # === 策略开关 ===
    short_market_24h_change_enable = BooleanParameter(default=True, space="buy", optimize=False)
    short_volume_enable = BooleanParameter(default=True, space="buy", optimize=False)
    short_atr_pct_enable = BooleanParameter(default=True, space="buy", optimize=False)

    # === 市场环境过滤参数 ===
    short_market_24h_change_min = DecimalParameter(
        -30.0, -5.0, default=-20.0, decimals=1, space="buy", optimize=optimize_entry
    )
    short_market_24h_change_max = DecimalParameter(
        5.0, 25.0, default=18.0, decimals=1, space="buy", optimize=optimize_entry
    )

    # ATR波动性过滤
    short_atr_pct_min = DecimalParameter(
        0.3, 1.0, default=0.5, decimals=1, space="buy", optimize=optimize_entry
    )
    short_atr_pct_max = DecimalParameter(
        3.0, 8.0, default=5.0, decimals=1, space="buy", optimize=optimize_entry
    )

    # 成交量过滤
    short_volume_factor = DecimalParameter(
        1.0, 2.0, default=1.2, decimals=1, space="buy", optimize=optimize_entry
    )

    # === 做空入场信号参数 ===
    short_rsi_fast = IntParameter(50, 80, default=65, space="buy", optimize=optimize_entry)
    short_rsi = IntParameter(55, 85, default=70, space="buy", optimize=optimize_entry)
    short_sma_ratio = DecimalParameter(
        1.01, 1.08, default=1.04, decimals=3, space="buy", optimize=optimize_entry
    )
    short_cti = DecimalParameter(
        -0.5, 1.0, default=0.2, decimals=2, space="buy", optimize=optimize_entry
    )

    # === 备用做空入场信号参数 ===
    short_rsi_fast_alt = IntParameter(55, 75, default=66, space="buy", optimize=optimize_entry)
    short_rsi_alt = IntParameter(65, 80, default=72, space="buy", optimize=optimize_entry)
    short_sma_ratio_alt = DecimalParameter(
        1.02, 1.06, default=1.04, decimals=3, space="buy", optimize=optimize_entry
    )

    # === 出场信号参数 ===
    sell_profit_protection_normal_fastk = IntParameter(
        5,
        30,
        default=16,
        space="sell",
        optimize=True,  # 反转原来的70-95
    )
    sell_cci_loss = IntParameter(-100, -60, default=-80, space="sell", optimize=True)
    sell_rsi_peak = IntParameter(15, 35, default=25, space="sell", optimize=True)
    sell_atr_max_pct = DecimalParameter(
        3.0, 8.0, default=5.0, decimals=1, space="sell", optimize=optimize_entry
    )

    # 时间止损参数
    sell_max_hold_hours = IntParameter(4, 12, default=8, space="sell", optimize=True)
    sell_max_hold_profit_threshold = DecimalParameter(
        -0.08, -0.02, default=-0.05, decimals=3, space="sell", optimize=True
    )

    # 利润保护参数
    sell_profit_protection_high_threshold = DecimalParameter(
        0.03, 0.08, default=0.05, decimals=3, space="sell", optimize=True
    )
    sell_profit_protection_high_fastk = IntParameter(
        15,
        35,
        default=25,
        space="sell",
        optimize=True,  # 反转原来的65-85
    )

    # === 动态杠杆调整超参数 ===
    # 基础杠杆设置
    short_leverage_atr_normal_vol = DecimalParameter(
        2.0, 4.0, default=3.0, decimals=1, space="buy", optimize=optimize_leverage_volume
    )
    short_leverage_atr_low_vol = DecimalParameter(
        4.0, 8.0, default=5.0, decimals=1, space="buy", optimize=optimize_leverage_volume
    )
    short_leverage_atr_high_vol = DecimalParameter(
        1.0, 3.0, default=2.0, decimals=1, space="buy", optimize=optimize_leverage_volume
    )
    # 波动性阈值
    short_leverage_atr_low_threshold = DecimalParameter(
        1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=optimize_leverage_threshold
    )
    short_leverage_atr_high_threshold = DecimalParameter(
        3.5, 5.5, default=3.5, decimals=1, space="buy", optimize=optimize_leverage_threshold
    )

    # 信号类型杠杆调整系数
    short_leverage_main_signal_multiplier = DecimalParameter(
        1.2, 1.6, default=1.0, decimals=2, space="buy", optimize=optimize_leverage_volume
    )
    short_leverage_alt_signal_multiplier = DecimalParameter(
        0.2, 0.6, default=0.33, decimals=2, space="buy", optimize=optimize_leverage_volume
    )

    # RSI极值杠杆调整
    short_leverage_rsi_extreme_enable = BooleanParameter(default=True, space="buy", optimize=False)
    short_leverage_rsi_extreme_threshold = DecimalParameter(
        72.0,
        85.0,
        default=80.0,
        space="buy",
        optimize=optimize_leverage_threshold,  # 反转原来的15-28
    )
    short_leverage_rsi_extreme_boost_factor = DecimalParameter(
        1.2, 1.6, default=1.3, decimals=2, space="buy", optimize=optimize_leverage_volume
    )

    # 成交量杠杆调整
    short_leverage_volume_enable = BooleanParameter(default=True, space="buy", optimize=False)
    short_leverage_volume_high_threshold = DecimalParameter(
        2.0, 4.0, default=3.0, decimals=1, space="buy", optimize=optimize_leverage_threshold
    )
    short_leverage_volume_high_reduction_factor = DecimalParameter(
        0.6, 0.9, default=0.8, decimals=2, space="buy", optimize=optimize_leverage_volume
    )

    # === 交易保护机制超参数 ===
    protection_stoploss_enable = BooleanParameter(default=True, space="protection", optimize=False)
    protection_stoploss_lookback_period = IntParameter(
        12, 48, default=24, space="protection", optimize=protection_stoploss_enable.value
    )
    protection_stoploss_trade_limit = IntParameter(
        1, 3, default=1, space="protection", optimize=protection_stoploss_enable.value
    )
    protection_stoploss_stop_duration = IntParameter(
        24, 120, default=60, space="protection", optimize=protection_stoploss_enable.value
    )

    @property
    def protections(self):
        """
        动态交易保护机制 - 基于超参数优化的智能保护策略
        """
        protection_list = []

        # 保护机制: 止损后暂停交易
        if self.protection_stoploss_enable.value:
            protection_list.append(
                {
                    "method": "StoplossGuard",
                    "lookback_period_candles": self.protection_stoploss_lookback_period.value,
                    "trade_limit": self.protection_stoploss_trade_limit.value,
                    "stop_duration_candles": self.protection_stoploss_stop_duration.value,
                    "only_per_pair": True,
                }
            )

        return protection_list

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """计算技术指标"""

        # === RSI系列指标 ===
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=14)
        dataframe["rsi_fast"] = ta.RSI(dataframe, timeperiod=4)
        dataframe["rsi_slow"] = ta.RSI(dataframe, timeperiod=20)

        # === 移动平均线 ===
        dataframe["sma_15"] = ta.SMA(dataframe, timeperiod=15)
        dataframe["ema_20"] = ta.EMA(dataframe, timeperiod=20)

        # === 趋势和动量指标 ===
        dataframe["cti"] = pta.cti(dataframe["close"], length=20)
        dataframe["cci"] = ta.CCI(dataframe, timeperiod=20)

        # === 随机指标 ===
        stoch_fast = ta.STOCHF(dataframe, 5, 3, 0, 3, 0)
        dataframe["fastk"] = stoch_fast["fastk"]
        dataframe["fastd"] = stoch_fast["fastd"]

        # === 波动性指标 ===
        dataframe["atr"] = ta.ATR(dataframe, timeperiod=14)
        dataframe["atr_pct"] = (dataframe["atr"] / dataframe["close"]) * 100

        # === 成交量指标 ===
        dataframe["volume_sma"] = ta.SMA(dataframe["volume"], timeperiod=20)
        dataframe["volume_ratio"] = dataframe["volume"] / dataframe["volume_sma"]

        # === 市场环境指标 ===
        # 24小时价格变化百分比
        dataframe["24h_change_pct"] = (
            (dataframe["close"] - dataframe["close"].shift(288)) / dataframe["close"].shift(288)
        ) * 100

        # === 布林带指标 ===
        bollinger = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2.0, nbdevdn=2.0, matype=0)
        dataframe["bb_lowerband"] = bollinger["lowerband"]
        dataframe["bb_middleband"] = bollinger["middleband"]
        dataframe["bb_upperband"] = bollinger["upperband"]
        dataframe["bb_percent"] = (dataframe["close"] - dataframe["bb_lowerband"]) / (
            dataframe["bb_upperband"] - dataframe["bb_lowerband"]
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """定义做空入场信号"""
        short_conditions = []
        dataframe.loc[:, "enter_tag"] = ""

        # === 市场环境过滤器 ===
        market_filter = True
        if self.short_market_24h_change_enable.value:
            market_filter = (
                (dataframe["24h_change_pct"].notna())
                & (dataframe["24h_change_pct"] > self.short_market_24h_change_min.value)
                & (dataframe["24h_change_pct"] < self.short_market_24h_change_max.value)
            )

        # === ATR波动性过滤器 ===
        atr_filter = True
        if self.short_atr_pct_enable.value:
            atr_filter = (dataframe["atr_pct"] > self.short_atr_pct_min.value) & (
                dataframe["atr_pct"] < self.short_atr_pct_max.value
            )

        # === 成交量过滤器 ===
        volume_filter = True
        if self.short_volume_enable.value:
            volume_filter = dataframe["volume_ratio"] > self.short_volume_factor.value

        # === 基础过滤条件组合 ===
        base_filter = market_filter & atr_filter & volume_filter

        # 主信号: 统一的超买回调逻辑
        rsi_trend_condition = dataframe["rsi_slow"] > dataframe["rsi_slow"].shift(1)

        main_short_entry = (
            base_filter
            & rsi_trend_condition
            & (dataframe["rsi_fast"] > self.short_rsi_fast.value)
            & (dataframe["rsi"] < self.short_rsi.value)
            & (dataframe["close"] > dataframe["sma_15"] * self.short_sma_ratio.value)
            & (dataframe["cti"] > self.short_cti.value)
            & (dataframe["bb_percent"] > 0.98)  # 极度接近布林带上轨
        )

        short_conditions.append(main_short_entry)
        dataframe.loc[main_short_entry, "enter_tag"] += "main_short"

        # 备用信号: 更激进的做空
        alt_short_entry = (
            base_filter
            & (dataframe["rsi_slow"] > dataframe["rsi_slow"].shift(1))
            & (dataframe["rsi_fast"] > self.short_rsi_fast_alt.value)
            & (dataframe["rsi"] < self.short_rsi_alt.value)
            & (dataframe["close"] > dataframe["sma_15"] * self.short_sma_ratio_alt.value)
            & (dataframe["cti"] > self.short_cti.value)
            & (dataframe["close"] > dataframe["bb_upperband"])  # 突破布林带上轨
        )

        short_conditions.append(alt_short_entry)
        dataframe.loc[alt_short_entry, "enter_tag"] += "alt_short"

        # === 应用入场条件 ===
        if short_conditions:
            dataframe.loc[reduce(lambda x, y: x | y, short_conditions), "enter_short"] = 1

        return dataframe

    def custom_exit(
        self,
        pair: str,
        trade: "Trade",
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        动态做空出场逻辑
        根据不同的入场原因实现智能出场
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()

        # === 时间止损检查 ===
        if current_time - trade.open_date_utc > timedelta(hours=self.sell_max_hold_hours.value):
            if current_profit > self.sell_max_hold_profit_threshold.value:
                return "max_hold_hours"

        # 盈利时的出场策略
        if current_profit > 0:
            # 高利润保护机制
            if current_profit > self.sell_profit_protection_high_threshold.value:
                if current_candle["fastk"] < self.sell_profit_protection_high_fastk.value:
                    return "high_profit_protection"

            # 标准盈利出场
            if current_candle["fastk"] < self.sell_profit_protection_normal_fastk.value:
                return "normal_profit_protection"

            # 针对主信号的特定出场
            if "main_short" in str(trade.enter_tag):
                if current_candle["rsi"] < self.sell_rsi_peak.value:
                    return "主信号保护"

        # 风险控制出场
        # 小幅亏损时的保护性出场
        if current_profit > -0.05:
            if current_candle["cci"] < self.sell_cci_loss.value:
                return "小幅亏损风险"

        # 趋势反转信号
        if current_profit > -0.03:
            if (
                current_candle["rsi"] < 30
                and current_candle["fastk"] < 20
                and current_candle["fastd"] < 25
            ):
                return "趋势反转信号"

        # 波动性异常退出
        if current_candle["atr_pct"] > self.sell_atr_max_pct.value * 1.5:
            if current_profit > -0.08:
                return "波动性异常退出"

        return None

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        静态出场信号（主要依赖custom_exit）
        """
        dataframe.loc[:, ["exit_short", "exit_tag"]] = (0, None)
        return dataframe

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time: datetime,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> bool:
        """
        交易确认函数 - 做空交易的最后一道风险控制
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return False

        current_candle = dataframe.iloc[-1].squeeze()

        # 确保关键指标存在且有效
        required_indicators = ["rsi", "rsi_fast", "atr_pct", "volume_ratio"]
        for indicator in required_indicators:
            if indicator not in current_candle or pd.isna(current_candle[indicator]):
                return False

        # 最终风险检查
        if current_candle["atr_pct"] > 10.0:  # 极端波动
            return False

        if current_candle["rsi"] < 10 or current_candle["rsi"] > 90:  # 极端RSI
            return False

        # 确保不在极端超卖状态（做空时避免极端超卖）
        if current_candle["rsi"] < 20 and current_candle["fastk"] < 10:
            return False

        return True

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态杠杆调整 - 基于超参数优化的智能杠杆策略（做空版本）
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return 1.0

        current_candle = dataframe.iloc[-1].squeeze()

        # === 第一步: 根据波动性确定基础杠杆 ===
        base_leverage = self.short_leverage_atr_normal_vol.value

        if "atr_pct" in current_candle and not pd.isna(current_candle["atr_pct"]):
            atr_pct = current_candle["atr_pct"]

            if atr_pct > self.short_leverage_atr_high_threshold.value:
                base_leverage = self.short_leverage_atr_high_vol.value
            elif atr_pct < self.short_leverage_atr_low_threshold.value:
                base_leverage = self.short_leverage_atr_low_vol.value

        # === 第二步: 根据入场信号类型调整杠杆 ===
        signal_multiplier = 1.0

        if "main_" in entry_tag:
            signal_multiplier = self.short_leverage_main_signal_multiplier.value
        elif "alt_" in entry_tag:
            signal_multiplier = self.short_leverage_alt_signal_multiplier.value

        adjusted_leverage = base_leverage * signal_multiplier

        # === 第三步: 最终杠杆限制和安全检查 ===
        final_leverage = max(1.0, min(round(adjusted_leverage, 2), max_leverage))

        # 额外的安全检查
        if "atr_pct" in current_candle and current_candle["atr_pct"] > 8.0:
            final_leverage = min(final_leverage, 1.5)

        return final_leverage

    def custom_stake_amount(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_stake: float,
        min_stake: float,
        max_stake: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态仓位管理 - 做空仓位策略
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return proposed_stake

        current_candle = dataframe.iloc[-1].squeeze()

        # 基础仓位
        base_stake = proposed_stake

        # 根据信号强度调整仓位
        if "main_" in entry_tag:
            position_multiplier = 1.0
        elif "alt_" in entry_tag:
            position_multiplier = 0.7
        else:
            position_multiplier = 0.8

        # 根据市场波动性调整仓位
        if "atr_pct" in current_candle:
            atr_pct = current_candle["atr_pct"]
            if atr_pct > 5.0:
                position_multiplier *= 1  # 高波动时保持仓位
            elif atr_pct < 1.0:
                position_multiplier *= 2  # 低波动时可以适当增加仓位

        # 根据RSI极值调整仓位
        if "rsi" in current_candle:
            rsi = current_candle["rsi"]
            if side == "short" and rsi > 80:
                position_multiplier *= 1.2  # 极度超买时可以稍微增加仓位

        final_stake = base_stake * position_multiplier
        return max(min_stake, min(final_stake, max_stake))
