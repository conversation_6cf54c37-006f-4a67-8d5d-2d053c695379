#!/usr/bin/env bash
# shellcheck disable=SC1091

# 显示帮助信息
show_help() {
    cat <<EOF
用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    --data-dir DIR          数据目录 (覆盖 DATA_DIR)
    --config-file FILE      配置文件 (覆盖 CONFIG_FILE)
示例:
    $0 --data-dir /path/to/data --config-file config/test.json

EOF
}

# 切换到脚本目录
cd "$(dirname "$0")" || exit

# 加载 .env 文件中的默认值
source .env

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
    -h | --help)
        show_help
        exit 0
        ;;
    --data-dir)
        DATA_DIR="$2"
        shift 2
        ;;
    --config-file)
        CONFIG_FILE="$2"
        shift 2
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 --help 查看可用选项"
        exit 1
        ;;
    esac
done

FREQTRADE_ABS=$(realpath "$FREQTRADE_PATH")
if [[ ! -f "$FREQTRADE_ABS" ]]; then
    echo "freqtrade文件不存在: $FREQTRADE_ABS"
    exit 1
fi

# 构建freqtrade命令
freqtrade_cmd=(
    "$FREQTRADE_ABS" webserver
    --datadir "${DATA_DIR}"
    --config "${CONFIG_FILE}"
)

session_name="webserver"

if tmux ls | grep "$session_name:" >/dev/null; then
    tmux -u attach -t "$session_name"
else
    tmux -u new-session -s "$session_name" -- "${freqtrade_cmd[@]}"
fi
