#!/usr/bin/env bash
# shellcheck disable=SC1091

# 显示帮助信息
show_help() {
    cat <<EOF
用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    --data-dir DIR          数据目录 (覆盖 DATA_DIR)
    --config-file FILE      配置文件 (覆盖 CONFIG_FILE)
    --start-date DATE       开始日期 (覆盖 START_DATE)
    --exchange NAME         交易所名称 (覆盖默认的binance)
    --timeframe TF          时间周期 (覆盖默认的时间周期)
    --pairs-file FILE       交易对文件 (覆盖默认设置)

示例:
    $0 --start-date 20240101
    $0 --data-dir /path/to/data --config-file config/test.json
    $0 --exchange kucoin --timeframe "1m 5m 15m"

EOF
}

# 切换到脚本目录
cd "$(dirname "$0")" || exit

# 加载 .env 文件中的默认值
source .env

# 默认设置
EXCHANGE="binance"
USE_PAIRS_FILE=true
PAIRS_FILE="${DATA_DIR}/pairs.json"
PREPEND=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
    -h | --help)
        show_help
        exit 0
        ;;
    --data-dir)
        DATA_DIR="$2"
        shift 2
        ;;
    --config-file)
        CONFIG_FILE="$2"
        USE_PAIRS_FILE=false
        shift 2
        ;;
    --start-date)
        START_DATE="$2"
        shift 2
        ;;
    --exchange)
        EXCHANGE="$2"
        shift 2
        ;;
    --prepend)
        PREPEND=true
        shift
        ;;
    --pairs-file)
        PAIRS_FILE="$2"
        USE_PAIRS_FILE=true
        shift 2
        ;;
    -p)
        PAIRS="$2"
        shift 2
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 --help 查看可用选项"
        exit 1
        ;;
    esac
done

FREQTRADE_ABS=$(realpath "$FREQTRADE_PATH")
if [[ ! -f "$FREQTRADE_ABS" ]]; then
    echo "freqtrade文件不存在: $FREQTRADE_ABS"
    exit 1
fi

# 显示当前配置
echo "=== 下载配置 ==="
echo "数据目录: ${DATA_DIR}"
echo "开始日期: ${START_DATE}"
echo "交易所: ${EXCHANGE}"
echo "================"

# 构建下载命令
freqtrade_cmd=(
    "$FREQTRADE_ABS" download-data
    --datadir "${DATA_DIR}"
    --data-format-ohlcv feather
    --data-format-trades feather
    --timerange="${START_DATE}-"
    --exchange "${EXCHANGE}"
    --timeframe 5m 15m 30m 1h
)

# 如果指定了交易对文件，添加到命令中
if [[ -n "$PAIRS" ]]; then
    echo "使用交易对: ${PAIRS}"
    freqtrade_cmd+=(--pairs "${PAIRS}")
elif [[ "$USE_PAIRS_FILE" == "true" && -f "${PAIRS_FILE}" ]]; then
    echo "使用交易对文件: ${PAIRS_FILE}"
    freqtrade_cmd+=(--pairs-file "${PAIRS_FILE}")
elif [[ -f "${CONFIG_FILE}" ]]; then
    echo "使用配置文件: ${CONFIG_FILE}"
    freqtrade_cmd+=(--config "${CONFIG_FILE}")
fi

# 执行命令
"${freqtrade_cmd[@]}"

if [ "$PREPEND" == "false" ]; then
    # 下载前面的数据
    freqtrade_cmd+=(--prepend)
    "${freqtrade_cmd[@]}"
fi
