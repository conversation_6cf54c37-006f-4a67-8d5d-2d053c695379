#!/usr/bin/env python3
"""
诊断E0V1E_ShortOnly策略为什么没有进单
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加策略路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'user_data', 'strategies'))

from E0V1E_ShortOnly import E0V1E_ShortOnly

def load_sample_data():
    """加载一些示例数据进行分析"""
    # 尝试加载真实数据
    data_dir = Path("../data/binance")
    
    # 查找可用的数据文件
    if data_dir.exists():
        json_files = list(data_dir.glob("*USDT*5m.json"))
        if json_files:
            print(f"找到数据文件: {json_files[0]}")
            try:
                df = pd.read_json(json_files[0])
                df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                df['date'] = pd.to_datetime(df['timestamp'], unit='ms')
                df = df.set_index('date')
                df = df.sort_index()
                print(f"加载了 {len(df)} 行真实数据")
                return df.tail(500)  # 取最近500条数据
            except Exception as e:
                print(f"加载真实数据失败: {e}")
    
    # 如果没有真实数据，创建模拟数据
    print("创建模拟数据进行分析...")
    dates = pd.date_range('2025-01-01', periods=500, freq='5min')
    
    # 创建一个上涨然后回调的价格模式
    base_price = 50000
    trend = np.linspace(0, 5000, 500)  # 上涨趋势
    noise = np.random.normal(0, 200, 500)  # 随机噪声
    prices = base_price + trend + noise
    
    # 在最后100个点添加一些超买的情况
    prices[-100:] = prices[-100:] + np.linspace(0, 2000, 100)
    
    df = pd.DataFrame({
        'open': prices * (1 + np.random.normal(0, 0.005, 500)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, 500))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, 500))),
        'close': prices,
        'volume': np.random.uniform(100, 1000, 500)
    }, index=dates)
    
    # 确保OHLC关系正确
    df['high'] = df[['open', 'high', 'low', 'close']].max(axis=1)
    df['low'] = df[['open', 'high', 'low', 'close']].min(axis=1)
    
    return df

def analyze_strategy_conditions(strategy, df):
    """分析策略条件"""
    print("\n=== 策略条件分析 ===")
    
    # 计算指标
    df_with_indicators = strategy.populate_indicators(df, {'pair': 'BTC/USDT'})
    
    # 检查关键指标的统计信息
    print("\n关键指标统计:")
    indicators = ['rsi', 'rsi_fast', 'rsi_slow', 'cti', 'atr_pct', 'volume_ratio', 'bb_percent']
    for indicator in indicators:
        if indicator in df_with_indicators.columns:
            series = df_with_indicators[indicator].dropna()
            if len(series) > 0:
                print(f"{indicator:12}: min={series.min():.3f}, max={series.max():.3f}, mean={series.mean():.3f}")
            else:
                print(f"{indicator:12}: 无有效数据")
        else:
            print(f"{indicator:12}: 指标不存在")
    
    # 计算入场信号
    df_with_entry = strategy.populate_entry_trend(df_with_indicators, {'pair': 'BTC/USDT'})
    
    # 分析入场条件
    print("\n=== 入场条件分析 ===")
    
    # 检查过滤条件
    print("\n过滤条件检查:")
    
    # 市场环境过滤
    if strategy.short_market_24h_change_enable.value:
        if '24h_change_pct' in df_with_entry.columns:
            change_pct = df_with_entry['24h_change_pct'].dropna()
            if len(change_pct) > 0:
                valid_market = (
                    (change_pct > strategy.short_market_24h_change_min.value) &
                    (change_pct < strategy.short_market_24h_change_max.value)
                ).sum()
                print(f"市场环境过滤: {valid_market}/{len(change_pct)} 条满足条件")
                print(f"  24h变化范围: {strategy.short_market_24h_change_min.value}% 到 {strategy.short_market_24h_change_max.value}%")
                print(f"  实际24h变化: min={change_pct.min():.2f}%, max={change_pct.max():.2f}%")
            else:
                print("市场环境过滤: 无24h变化数据")
        else:
            print("市场环境过滤: 缺少24h_change_pct指标")
    
    # ATR波动性过滤
    if strategy.short_atr_pct_enable.value:
        if 'atr_pct' in df_with_entry.columns:
            atr_pct = df_with_entry['atr_pct'].dropna()
            if len(atr_pct) > 0:
                valid_atr = (
                    (atr_pct > strategy.short_atr_pct_min.value) &
                    (atr_pct < strategy.short_atr_pct_max.value)
                ).sum()
                print(f"ATR波动性过滤: {valid_atr}/{len(atr_pct)} 条满足条件")
                print(f"  ATR范围: {strategy.short_atr_pct_min.value}% 到 {strategy.short_atr_pct_max.value}%")
                print(f"  实际ATR: min={atr_pct.min():.2f}%, max={atr_pct.max():.2f}%")
            else:
                print("ATR波动性过滤: 无ATR数据")
        else:
            print("ATR波动性过滤: 缺少atr_pct指标")
    
    # 成交量过滤
    if strategy.short_volume_enable.value:
        if 'volume_ratio' in df_with_entry.columns:
            volume_ratio = df_with_entry['volume_ratio'].dropna()
            if len(volume_ratio) > 0:
                valid_volume = (volume_ratio > strategy.short_volume_factor.value).sum()
                print(f"成交量过滤: {valid_volume}/{len(volume_ratio)} 条满足条件")
                print(f"  成交量倍数要求: > {strategy.short_volume_factor.value}")
                print(f"  实际成交量比率: min={volume_ratio.min():.2f}, max={volume_ratio.max():.2f}")
            else:
                print("成交量过滤: 无成交量数据")
        else:
            print("成交量过滤: 缺少volume_ratio指标")
    
    # 检查主信号条件
    print("\n主信号条件检查:")
    last_rows = df_with_entry.tail(50)  # 检查最后50行
    
    for i, (idx, row) in enumerate(last_rows.iterrows()):
        if pd.isna(row.get('rsi')) or pd.isna(row.get('rsi_fast')):
            continue
            
        conditions = []
        
        # RSI趋势条件
        if i > 0:
            prev_rsi_slow = last_rows.iloc[i-1].get('rsi_slow', 0)
            rsi_trend = row.get('rsi_slow', 0) > prev_rsi_slow
            conditions.append(f"RSI趋势: {rsi_trend}")
        
        # RSI条件
        rsi_fast_ok = row.get('rsi_fast', 0) > strategy.short_rsi_fast.value
        rsi_ok = row.get('rsi', 0) < strategy.short_rsi.value
        conditions.append(f"RSI_fast({row.get('rsi_fast', 0):.1f}) > {strategy.short_rsi_fast.value}: {rsi_fast_ok}")
        conditions.append(f"RSI({row.get('rsi', 0):.1f}) < {strategy.short_rsi.value}: {rsi_ok}")
        
        # SMA条件
        sma_ok = row.get('close', 0) > row.get('sma_15', 0) * strategy.short_sma_ratio.value
        conditions.append(f"价格 > SMA*{strategy.short_sma_ratio.value}: {sma_ok}")
        
        # CTI条件
        cti_ok = row.get('cti', 0) > strategy.short_cti.value
        conditions.append(f"CTI({row.get('cti', 0):.3f}) > {strategy.short_cti.value}: {cti_ok}")
        
        # 布林带条件
        bb_ok = row.get('bb_percent', 0) > 0.8
        conditions.append(f"BB%({row.get('bb_percent', 0):.3f}) > 0.8: {bb_ok}")
        
        all_ok = all([rsi_fast_ok, rsi_ok, sma_ok, cti_ok, bb_ok])
        if all_ok or i >= len(last_rows) - 5:  # 显示最后5行或满足条件的行
            print(f"  时间 {idx}: {', '.join(conditions)} -> 满足: {all_ok}")
    
    # 检查是否有入场信号
    if 'enter_short' in df_with_entry.columns:
        short_signals = df_with_entry['enter_short'].sum()
        print(f"\n总做空信号数量: {short_signals}")
        
        if short_signals > 0:
            signal_rows = df_with_entry[df_with_entry['enter_short'] == 1]
            print("做空信号详情:")
            for idx, row in signal_rows.iterrows():
                print(f"  {idx}: RSI={row.get('rsi', 0):.1f}, RSI_fast={row.get('rsi_fast', 0):.1f}, CTI={row.get('cti', 0):.3f}")
    else:
        print("\n没有找到enter_short列")
    
    return df_with_entry

def main():
    print("开始诊断E0V1E_ShortOnly策略...")
    
    # 创建策略实例
    config = {
        'timeframe': '5m',
        'stake_currency': 'USDT',
        'stake_amount': 100,
        'dry_run': True,
        'trading_mode': 'futures',
        'margin_mode': 'isolated',
    }
    
    strategy = E0V1E_ShortOnly(config)
    
    # 显示策略参数
    print("\n=== 策略参数 ===")
    print(f"做空RSI阈值: {strategy.short_rsi.value}")
    print(f"做空RSI_fast阈值: {strategy.short_rsi_fast.value}")
    print(f"做空SMA比率: {strategy.short_sma_ratio.value}")
    print(f"做空CTI阈值: {strategy.short_cti.value}")
    print(f"ATR范围: {strategy.short_atr_pct_min.value}% - {strategy.short_atr_pct_max.value}%")
    print(f"24h变化范围: {strategy.short_market_24h_change_min.value}% - {strategy.short_market_24h_change_max.value}%")
    
    # 加载数据
    df = load_sample_data()
    
    # 分析策略条件
    df_result = analyze_strategy_conditions(strategy, df)
    
    print("\n=== 建议 ===")
    print("如果没有进单，可能的原因:")
    print("1. RSI条件过于严格 - 考虑调整short_rsi和short_rsi_fast参数")
    print("2. 市场环境过滤过于严格 - 检查24h_change_pct范围")
    print("3. ATR波动性过滤过于严格 - 检查atr_pct范围")
    print("4. 成交量过滤过于严格 - 检查volume_ratio要求")
    print("5. 布林带条件过于严格 - bb_percent > 0.8可能很少满足")
    
    print("\n建议的参数调整:")
    print("- 降低short_rsi从79到75")
    print("- 降低short_rsi_fast从62到55")
    print("- 降低bb_percent阈值从0.8到0.7")
    print("- 放宽ATR范围或24h变化范围")

if __name__ == "__main__":
    main()
