# E0V1E_ShortOnly 纯做空策略

## 策略概述

E0V1E_ShortOnly 是基于 E0V1E_Enhanced 策略改造的纯做空策略，专门用于捕捉市场的超买回调机会。

### 核心特性

1. **纯做空交易**: 只进行做空操作，不做多
2. **超买回调逻辑**: 基于多重技术指标确认的超买状态入场
3. **双重入场信号**: 主信号和备用信号两种入场模式
4. **智能风险管理**: 动态杠杆调整和仓位管理
5. **全面过滤机制**: 市场环境、波动性、成交量多重过滤

## 策略逻辑

### 入场条件

#### 主信号 (main_short)
- RSI慢线上升趋势
- RSI快速 > 65 (超买)
- RSI > 70 (确认超买)
- 价格 > SMA15 * 1.04 (价格高于均线)
- CTI > 0.2 (动量确认)
- 布林带百分位 > 0.8 (接近上轨)

#### 备用信号 (alt_short)
- RSI慢线上升趋势
- RSI快速 > 66 (更激进的超买)
- RSI > 72 (更强的超买确认)
- 价格 > SMA15 * 1.04 (价格高于均线)
- CTI > 0.2 (动量确认)
- 价格 > 布林带上轨 (突破上轨)

### 过滤条件

1. **市场环境过滤**
   - 24小时价格变化: -20% 到 +18%
   - 避免极端市场条件

2. **波动性过滤**
   - ATR百分比: 0.5% 到 5.0%
   - 确保适度波动性

3. **成交量过滤**
   - 成交量比率 > 1.2
   - 确保足够的流动性

### 出场条件

1. **盈利保护**
   - 高利润保护: 利润 > 8% 且 FastK < 25
   - 标准盈利保护: FastK < 16

2. **风险控制**
   - 小幅亏损保护: 亏损 < 5% 且 CCI < -80
   - 趋势反转: RSI < 30 且 FastK < 20

3. **时间止损**
   - 最大持仓时间: 8小时
   - 亏损阈值: -5%

## 使用方法

### 1. 基本回测

```bash
freqtrade backtesting --config config_short_only.json --strategy E0V1E_ShortOnly --timerange 20240101-20241201
```

### 2. 参数优化

```bash
freqtrade hyperopt --config config_short_only.json --strategy E0V1E_ShortOnly --hyperopt-loss SharpeHyperOptLoss --spaces buy sell --epochs 100
```

### 3. 模拟交易

```bash
freqtrade trade --config config_short_only.json --strategy E0V1E_ShortOnly
```

## 风险管理

### 杠杆控制
- 基础杠杆: 3倍
- 低波动时: 最高5倍
- 高波动时: 最低2倍
- 主信号: 标准杠杆
- 备用信号: 0.33倍杠杆

### 仓位管理
- 主信号: 100%仓位
- 备用信号: 70%仓位
- 根据波动性动态调整

### 保护机制
- 止损保护: 连续止损后暂停交易
- 追踪止损: 利润15%后激活
- 固定止损: -10%

## 注意事项

1. **市场适用性**: 适合震荡和下跌市场，牛市中需谨慎使用
2. **时间框架**: 专为5分钟时间框架设计
3. **交易对选择**: 建议选择流动性好的主流币种
4. **风险控制**: 严格遵守止损规则，控制单笔交易风险

## 参数调优建议

### 入场参数
- `short_rsi`: 调整RSI超买阈值 (55-85)
- `short_rsi_fast`: 调整快速RSI阈值 (50-80)
- `short_sma_ratio`: 调整价格与均线比率 (1.01-1.08)

### 出场参数
- `sell_profit_protection_normal_fastk`: 调整标准出场阈值 (5-30)
- `sell_rsi_peak`: 调整RSI出场阈值 (15-35)
- `sell_max_hold_hours`: 调整最大持仓时间 (4-12小时)

### 过滤参数
- `short_atr_pct_min/max`: 调整波动性过滤范围
- `short_volume_factor`: 调整成交量过滤阈值
- `short_market_24h_change_min/max`: 调整市场环境过滤

## 免责声明

本策略仅供学习和研究使用，不构成投资建议。加密货币交易存在高风险，可能导致本金损失。请在充分了解风险的前提下使用，并建议先在模拟环境中测试。
